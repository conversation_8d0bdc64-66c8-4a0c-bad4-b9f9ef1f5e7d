import { useState, useEffect } from 'react';
import { Link, Navigate } from 'react-router-dom';
import {
  FaUser, FaGamepad, FaCog, FaHeart, FaHistory,
  FaSignOutAlt, FaCreditCard, FaShoppingCart,
  FaChevronRight
} from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { useLanguageNavigation } from '../hooks/useLanguageNavigation';
import LoadingSpinner from '../components/LoadingSpinner';
import { API_URL } from '../config/env.js';
import { gamePlaceholder } from '../assets/placeholders.js';
import { getSecureImageUrl } from '../utils/imageUtils';

// Import Profile Components
import OverviewSection from '../components/Profile/OverviewSection';
import LibrarySection from '../components/Profile/LibrarySection';
import SettingsSection from '../components/Profile/SettingsSection';
import WishlistSection from '../components/Profile/WishlistSection';
import PurchasesSection from '../components/Profile/PurchasesSection';
import UploadsSection from '../components/Profile/UploadsSection';

const ProfilePage = () => {
  const { user, logout } = useAuth();
  const { createLanguageLink } = useLanguageNavigation();
  const [activeSection, setActiveSection] = useState('overview');
  const [games, setGames] = useState([]);
  const [wishlist, setWishlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [credits, setCredits] = useState(0);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [userData, setUserData] = useState(null); // Added to store complete user data

  useEffect(() => {
    if (user) {
      // Fetch the complete user profile including bio
      const fetchUserProfile = async () => {
        try {
          const response = await fetch(`${API_URL}/users/profile`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (response.ok) {
            const profileData = await response.json();
            setUserData(profileData);
          } else {
            console.error('Failed to fetch user profile');
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      };

      // Fetch user data
      fetchUserProfile();
      
      // Simulate fetching game data from API (keep existing code)
      setTimeout(() => {
        setGames(sampleGames);
        setWishlist(sampleWishlist);
        setCredits(350); // Sample credits
        setLoading(false);
      }, 800);
    }
  }, [user]);

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
    }
  };

  const toggleMobileNav = () => {
    setMobileNavOpen(!mobileNavOpen);
  };

  // Render the content based on active section
  const renderContent = () => {
    switch(activeSection) {
      case 'overview':
        return <OverviewSection user={userData || user} games={games} credits={credits} setActiveSection={setActiveSection} createLanguageLink={createLanguageLink} />;
      case 'library':
        return <LibrarySection games={games} />;
      case 'settings':
        return <SettingsSection user={userData || user} />;
      case 'wishlist':
        return <WishlistSection wishlist={wishlist} />;
      case 'purchases':
        return <PurchasesSection />;
      case 'uploads':
        return <UploadsSection />;
      default:
        return <OverviewSection user={userData || user} games={games} credits={credits} setActiveSection={setActiveSection} createLanguageLink={createLanguageLink} />;
    }
  };

  return (
    <div className="min-h-[calc(100vh-60px)] bg-gray-900 p-5 text-gray-200">
      <div className="max-w-7xl mx-auto flex gap-8 relative">
        {/* Mobile Navigation Toggle */}
        <button 
          className="hidden max-lg:block w-full bg-gray-800 text-white p-4 border-none text-left text-lg font-semibold cursor-pointer rounded mb-5 relative"
          onClick={toggleMobileNav}
        >
          {activeSection} 
          <FaChevronRight className={`absolute right-4 transition-transform duration-300 ${mobileNavOpen ? 'rotate-90' : ''}`} />
        </button>

        {/* Sidebar Navigation */}
        <aside className={`w-72 bg-gray-800 rounded-lg shadow-xl p-5 sticky top-20 h-fit max-lg:fixed max-lg:top-0 max-lg:left-0 max-lg:h-full max-lg:z-50 max-lg:transform max-lg:transition-transform max-lg:duration-300 ${mobileNavOpen ? 'max-lg:translate-x-0' : 'max-lg:-translate-x-full'}`}>
          <div className="flex flex-col items-center py-5 border-b border-gray-600">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4 overflow-hidden border-2 border-gray-600">
              {user.avatar ? (
                <img src={getSecureImageUrl(user.avatar)} alt={user.username} className="w-full h-full object-cover" />
              ) : (
                <FaUser className="text-5xl text-gray-500" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-white mb-1">{user.username}</h2>
            <div className="flex items-center gap-2 bg-yellow-500/15 text-yellow-400 px-3 py-1 rounded-full font-semibold mt-2">
              <FaCreditCard /> {credits} Credits
            </div>
          </div>
          
          <nav className="mt-5">
            <ul className="list-none p-0">
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'overview' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('overview'); setMobileNavOpen(false);}}
                >
                  <FaUser /> Overview
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'library' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('library'); setMobileNavOpen(false);}}
                >
                  <FaGamepad /> Game Library
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'wishlist' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('wishlist'); setMobileNavOpen(false);}}
                >
                  <FaHeart /> Wishlist
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'purchases' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('purchases'); setMobileNavOpen(false);}}
                >
                  <FaHistory /> Purchase History
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'uploads' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('uploads'); setMobileNavOpen(false);}}
                >
                  <FaShoppingCart /> My Uploads
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'settings' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('settings'); setMobileNavOpen(false);}}
                >
                  <FaCog /> Settings
                </button>
              </li>
              <li className="mt-3">
                <button 
                  className="flex items-center gap-3 w-full p-3 border-none bg-transparent text-red-400 text-left text-base rounded-md cursor-pointer transition-all duration-200 hover:bg-white/5"
                  onClick={handleLogout}
                >
                  <FaSignOutAlt /> Logout
                </button>
              </li>
            </ul>
          </nav>
          
          <div className="mt-8 pt-5 border-t border-gray-600 text-center text-gray-500 text-sm">
            <p>Member since {formatDate(user.joinDate || new Date())}</p>
            <Link 
              to="/buy-credits" 
              className="inline-block bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold mt-3 transition-all duration-300 shadow-lg shadow-red-500/30 hover:-translate-y-0.5 hover:shadow-red-500/40 no-underline"
            >
              Buy Credits
            </Link>
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 bg-gray-800 rounded-lg shadow-xl p-6 min-h-[600px]">
          {loading ? (
            <div className="w-full flex flex-col items-center justify-center py-24">
              <LoadingSpinner size="lg" color="primary" showText={true} text="Loading your profile data..." />
            </div>
          ) : (
            renderContent()
          )}
        </main>
      </div>
    </div>
  );
};





// Helper function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Sample data for testing
const sampleGames = [
  {
    id: 1,
    title: "Space Explorer",
    image: gamePlaceholder,
    genre: "adventure",
    paymentType: "paid",
    price: "$9.99",
    tags: ["space", "exploration", "sci-fi"]
  },
  {
    id: 2,
    title: "Fantasy Quest",
    image: gamePlaceholder,
    genre: "rpg",
    paymentType: "credits",
    price: "150 Credits",
    tags: ["fantasy", "rpg", "adventure"]
  },
  {
    id: 3,
    title: "Puzzle Master",
    image: gamePlaceholder,
    genre: "puzzle",
    paymentType: "free",
    price: "Free",
    tags: ["puzzle", "strategy"]
  },
  {
    id: 4,
    title: "Zombie Survival",
    image: gamePlaceholder,
    genre: "action",
    paymentType: "paid",
    price: "$4.99",
    tags: ["zombies", "survival", "action"]
  }
];

const sampleWishlist = [
  {
    id: 5,
    title: "Racing Champions",
    image: gamePlaceholder,
    genre: "sports",
    paymentType: "paid",
    price: "$12.99",
    tags: ["racing", "sports", "multiplayer"]
  },
  {
    id: 6,
    title: "Medieval Kingdom",
    image: gamePlaceholder,
    genre: "strategy",
    paymentType: "credits",
    price: "200 Credits",
    tags: ["strategy", "medieval", "building"]
  },
  {
    id: 7,
    title: "Pixel Platformer",
    image: gamePlaceholder,
    genre: "platformer",
    paymentType: "free",
    price: "Free",
    tags: ["pixel-art", "platformer", "indie"]
  }
];
export default ProfilePage;
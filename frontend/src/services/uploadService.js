import axios from 'axios';

import { API_URL } from '../config/env.js';

// Create an instance of axios with baseURL
const uploadApi = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Use cookies for authentication
  headers: {
    'Content-Type': 'multipart/form-data' // For file uploads
  }
});

// Remove auth token interceptor since we're using cookies
uploadApi.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => Promise.reject(error)
);

// Simplified to handle single game file upload with progress tracking
export const uploadGame = async (
  gameData,
  gameFile, // Single game file
  coverImage, // Not used anymore but kept for compatibility
  cardImage,
  gifAnimation,
  screenshots, // Not used anymore but kept for compatibility
  onProgress = null // Progress callback function
) => {
  try {
    // Create FormData object to handle file uploads
    const formData = new FormData();
    
    // Add game metadata as JSON string
    formData.append('gameData', JSON.stringify(gameData));
    
    // Handle single game file
    if (gameFile) {
      formData.append('gameFile', gameFile);
    }
    
    // Add other files (only card image and GIF now)
    if (cardImage) {
      formData.append('cardImage', cardImage);
    }

    if (gifAnimation) {
      formData.append('gifAnimation', gifAnimation);
    }
    
    // Make API call to upload the game with progress tracking
    const response = await axios.post(`${API_URL}/upload/game`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      withCredentials: true, // Use cookies for authentication
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading game:', error);
    throw error.response?.data || { message: 'Network error during upload' };
  }
};

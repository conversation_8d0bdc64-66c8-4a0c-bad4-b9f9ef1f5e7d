import { FaHistory } from 'react-icons/fa';

// Helper function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

const PurchasesSection = () => {
  const purchases = [
    { id: 1, gameName: "Space Explorer", date: "2023-10-15", amount: "$9.99", status: "completed" },
    { id: 2, gameName: "Fantasy Quest", date: "2023-09-22", amount: "150 Credits", status: "completed" },
    { id: 3, gameName: "Credits Bundle 500", date: "2023-09-05", amount: "$4.99", status: "completed" }
  ];
  
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Purchase History</h1>
      
      {purchases.length > 0 ? (
        <div className="bg-gray-700/30 rounded-lg border border-gray-600 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-600">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Date</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Item</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Amount</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Status</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-600">
                {purchases.map(purchase => (
                  <tr key={purchase.id} className="hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="px-6 py-4 text-sm text-gray-300">{formatDate(purchase.date)}</td>
                    <td className="px-6 py-4 text-sm text-white font-medium">{purchase.gameName}</td>
                    <td className="px-6 py-4 text-sm text-gray-300">{purchase.amount}</td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        purchase.status === 'completed' 
                          ? 'bg-green-500/20 text-green-300' 
                          : 'bg-yellow-500/20 text-yellow-300'
                      }`}>
                        {purchase.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <button className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                        Receipt
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaHistory className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No purchase history</h3>
          <p className="text-gray-400">Your purchases will appear here</p>
        </div>
      )}
    </section>
  );
};

export default PurchasesSection;
